package models

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/content"
	"encoding/json"
	"time"

	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
)

/*
files (

	id              uuid primary key,             -- or ULID/Snowflake
	tenant_id       uuid not null,
	bucket          text not null,
	s3_key          text not null unique,         -- server-chosen
	status          text not null,                -- pending | uploaded | scanning | clean | quarantined | failed
	size_bytes      bigint,
	content_type    text,
	etag            text,
	checksum_sha256 text,
	filename        text,                         -- original display name
	uploader_id     uuid not null,
	metadata        jsonb not null default '{}',  -- keywords, description, etc.
	created_at      timestamptz not null default now(),
	completed_at    timestamptz

)
*/

type (
	File struct {
		commonModels.Entity
		content.Base
		commonModels.Trackable

		StorageID string // s3://bucket/p/a/t/h
		FolderID  uuid.UUID

		ETag   string // from S3
		Hash   string // SHA256 of the file
		Status string // pending | uploaded | scanning | clean | quarantined | failed
		Active bool

		Filename    string
		Title       string
		Alt         string
		Keywords    dbDriver.PgStringArray `gorm:"type:text[]"`
		Description string
		Text        string
		Tags        dbDriver.PgUUIDArray `gorm:"type:uuid[]"`

		Type        string // image, document, video, audio
		ContentType string
		Extension   string
		FileSize    int64
		Width       int
		Height      int

		Meta     datatypes.JSONType[map[string]string]
		Settings json.RawMessage

		StructureID *uuid.UUID
		Data        json.RawMessage

		CropIDs      dbDriver.PgUUIDArray `gorm:"type:uuid[]"`
		DirtyCropIDs dbDriver.PgUUIDArray `gorm:"type:uuid[]"`

		ProcessedAt  *time.Time
		ProcessorLog datatypes.JSONType[ProcessorResults]
	}

	ProcessorResults struct {
		Retries int
		Log     []ProcessorLog
	}

	ProcessorLog struct {
		Time  time.Time
		Error string
	}
)
