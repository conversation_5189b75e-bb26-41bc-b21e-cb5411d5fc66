

```mermaid
sequenceDiagram
    autonumber
    participant FE as Frontend (React)
    participant B<PERSON> as Backend (Go)
    participant DB as DB (uploads, files)
    participant S3 as Amazon S3
    participant T<PERSON> as Tika Server

    Note over FE: User selects file, finishes form, and clicks “Upload”
    FE->>FE: FE computes SHA-256, size, type
    FE->>BE: POST /api/v2/storage/upload-policy<br/>{fileName, contentType, sha256, fileSize}
    BE->>BE: Generate S3 key
    BE->>S3: Create presigned (PUT or POST) URL/fields
    BE->>DB: INSERT into uploads (key, sha256, size, status='pending', ...)
    BE-->>FE: {key, presignedUrl (+fields), maxSize, constraints}

    FE->>S3: Upload file (single-part or multipart)
    S3-->>FE: 200/204 Uploaded

    FE->>BE: POST /api/v2/storage/upload<br/>{key, formData, clientMeta...}
    BE->>DB: UPDATE uploads SET status='completed', completed_at=now()
    BE->>DB: INSERT INTO files (key, contentType, size, sha256, user meta, ...)
    BE-->>FE: 200 OK {fileId}

    par async metadata extraction
      BE->>Tika: GET/PUT stream S3 object → Tika
      Tika-->>BE: Parsed metadata (mime verified, img dims, extracted text, etc.)
      BE->>DB: UPDATE files SET contentType, width/height, text, ...
    end

```
