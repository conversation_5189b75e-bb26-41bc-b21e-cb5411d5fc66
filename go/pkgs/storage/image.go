package storage

import (
	"contentmanager/etc/conf"
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/image_crop_size"
	"fmt"
	"io"
	"strings"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type File struct {
	Reader      io.ReadCloser
	FileName    string
	ContentType string
}

func GetImage(r *shared.AppContext, filename, size, crop string) (*File, error) {
	// Get the media object
	media, err := getImageByNameOrID(r, filename)
	if err != nil {
		return nil, err
	}

	var cropSize image_crop_size.ImageCropSize
	if len(crop) > 0 {
		size = "crop"
		cropSize, err = image_crop_size.GetImageCropSizeByNameOrID(r, crop)

		if err != nil {
			return nil, err
		}
	}

	var key string
	switch size {
	case "full":
		key = fmt.Sprintf("%s/full/%s_full", r.TenantID(), media.ID)
	case "crop":
		key = fmt.Sprintf("%s/full/%s_%s", r.Tenant<PERSON>(), media.ID, cropSize.ID)
	default:
		key = fmt.Sprintf("%s/thumbnail/%s_thumbnail", r.TenantID(), media.ID)
	}

	//config := cache.ICacheAdapter().GetObject(conf.TenancyConfigCacheKey).(config.AppConfig)
	appConfig := config.GetAppConfig()
	rc, err := Download(conf.AwsS3Client, appConfig.AwsBucket, key)
	if err != nil {
		return nil, err
	}

	// Content type
	last4 := strings.ToLower(media.Filename[len(media.Filename)-4:])

	var contentType string
	switch last4 {
	case ".png":
		contentType = "image/png; charset=utf-8"
		break
	case ".svg":
		contentType = "image/svg+xml; charset=utf-8"
		break
	case "jpeg", ".jpg", "jfif", ".bmp":
		contentType = "image/jpeg; charset=utf-8"
		break
	case ".gif":
		contentType = "image/gif; charset=utf-8"
		break
	default:
		contentType = "image/png; charset=utf-8"
	}

	return &File{
		Reader:      rc,
		FileName:    media.Filename,
		ContentType: contentType,
	}, nil
}

func getImageByNameOrID(r *shared.AppContext, filename string) (commonModels.Media, error) {
	dbQuery := r.TenantDatabase()

	id := uuid.FromStringOrNil(filename)
	if id == uuid.Nil {
		dbQuery = dbQuery.
			Where(" lower(filename) = lower(?) ", filename)
	} else {
		dbQuery = dbQuery.Where(" id = ? ", id)
	}

	var mm []commonModels.Media
	if err := dbQuery.
		Where(" active = true ").
		Where(" type = 'image' ").
		Find(&mm).
		Error; err != nil {
		return commonModels.Media{}, err
	}

	if len(mm) == 0 {
		return commonModels.Media{}, gorm.ErrRecordNotFound
	}
	if len(mm) == 1 {
		return mm[0], nil
	}

	for _, m := range mm {
		if slicexx.Contains(m.Sites, r.CurrentSiteID()) {
			return m, nil
		}
	}

	return mm[0], nil
}
