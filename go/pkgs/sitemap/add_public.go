package sitemap

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	publicControllers "contentmanager/library/tenant/public/controllers"
	"fmt"
	"net/http"
)

func AddPublicRoute(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Get("/sitemap.txt", GET_TXT)
	r.Get("/sitemap.xml", GET_XML)
	r.Get("/robots.txt", getRobotsTXT)
	return r
}

func GET_TXT(w http.ResponseWriter, r *shared.AppContext) {
	if r.TenantDatabase() == nil {
		http.NotFound(w, r.Request())
		return
	}
	publicControllers.WriteWithStandardHeaders(w, r, buildTXT(r.TenantDatabase(), r.Request().Host, r.CurrentSiteID()))
}

func GET_XML(w http.ResponseWriter, r *shared.AppContext) {
	if r.TenantDatabase() == nil {
		http.NotFound(w, r.Request())
		return
	}
	if data, err := buildXML(r.TenantDatabase(), r.Request().Host, r.CurrentSiteID()); err != nil {
		http.Error(w, "unable to generate sitemap", http.StatusBadRequest)
	} else {
		publicControllers.WriteWithStandardHeaders(w, r, data)
	}
}

func getRobotsTXT(w http.ResponseWriter, r *shared.AppContext) {
	var sitemapEntry string
	var allowDisallowEntry string

	if publicControllers.IsStagingDomain(r.Request().RequestURI) {
		allowDisallowEntry = "Disallow: /"
	} else {
		allowDisallowEntry = "Allow: /"
		sitemapEntry = "Sitemap: https://" + r.Request().Host + "/sitemap.xml"
	}
	var response = fmt.Sprintf("User-agent: *\n%s\n%s", allowDisallowEntry, sitemapEntry)
	publicControllers.WriteWithStandardHeaders(w, r, []byte(response))
}
