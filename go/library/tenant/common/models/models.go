package commonModels

import (
	"encoding/json"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm/schema"
)

type (
	IContent interface {
		GetSites() []uuid.UUID
		GetDepartmentID() *uuid.UUID
		GetType() string
	}

	IContentTabler interface {
		IContent
		schema.Tabler
	}

	IRoute interface {
		GetRoute() string
	}

	IContentRoute interface {
		IEntity
		IContent
		IRoute
		GetPublished() bool
	}

	IEntity interface {
		GetID() uuid.UUID
	}

	IContentEntityTabler interface {
		IContentTabler
		IEntity
	}

	Entity struct {
		ID uuid.UUID `gorm:"column:id;type:uuid; primaryKey; not null; default:uuid_generate_v4();"`
	}

	BaseContent struct {
		Type         string      `json:"type" binding:"required"`
		Sites        []uuid.UUID `json:"sites" binding:"required"`
		DepartmentID *uuid.UUID  `json:"department_id"`
	}

	XData struct {
		Data json.RawMessage `gorm:"column:data;type:jsonb;"`
	}
)

func (e Entity) GetID() uuid.UUID {
	return e.ID
}

func (b BaseContent) GetSites() []uuid.UUID {
	return b.Sites
}

func (b BaseContent) GetDepartmentID() *uuid.UUID {
	return b.DepartmentID
}

func (b BaseContent) GetType() string {
	return b.Type
}

func (b BaseContent) GetScopeEntity() string {
	switch b.Type {
	case "template", "css", "js":
		return "cm.resource." + b.Type
	case "page", "distributed_page", "external_link":
		return "cm.content.page"
	default:
		return "cm.content." + b.Type
	}
}

var _ IContent = (*BaseContent)(nil)
var _ IEntity = (*Entity)(nil)
