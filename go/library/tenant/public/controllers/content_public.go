package publicControllers

import (
	"contentmanager/etc/conf"
	"contentmanager/library/shared"
	"contentmanager/library/tenant/common/services"
	"contentmanager/library/tenant/public/features/content"
	"contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/services"
	"contentmanager/library/utils"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/mapxx"
	"contentmanager/library/utils/urls"
	"contentmanager/logging"
	"contentmanager/pkgs/auth_links"
	"contentmanager/pkgs/csp"
	"encoding/json"
	"errors"
	"net/http"
	"net/url"
	"strings"
	"time"

	ics "github.com/arran4/golang-ical"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type ContentController struct{}

/*
************************************************************************************************************
Public Webserver Content Controller
************************************************************************************************************
*/
func (cc ContentController) GetContentPage(w http.ResponseWriter, r *shared.AppContext) {
	if r.TenantDatabase() == nil {
		http.NotFound(w, r.Request())
		return
	}
	var result, err = publicServices.GetContentResults(r)
	ApplyStagingHeaders(w, r)

	if err != nil {
		if errors.Is(err, publicServices.ErrPermissionRequired) {
			publicServices.AccessDenied(w, r)
			return
		}
		if u, ok := checkRedirects(r.TenantDatabase(), r.URL()); ok {
			http.Redirect(w, r.Request(), u.String(), 301)
			return
		}
		r.Logger().Log().
			Err(err).
			Str("tenant", r.Tenant().Name).
			Msg("error getting page")
		if !isRequestForPageType(r.Request().URL.Path) {
			http.NotFound(w, r.Request())
			return
		}
		if result, err = cc.Get404Page(r); err != nil {
			http.NotFound(w, r.Request())
			return
		} else {
			w.WriteHeader(http.StatusNotFound)
			_, _ = w.Write([]byte(result))
			return
		}
	}

	WriteWithStandardHeaders(w, r, []byte(result))
}

func (cc ContentController) Get404Page(r *shared.AppContext) (string, error) {
	//Allow Anonymous
	return publicServices.CompileTemplate(r, nil, publicServices.NotFoundTemplate)
}

func (cc ContentController) GetAlerts(w http.ResponseWriter, r *shared.AppContext) {
	//Allow Anonymous
	alertType := r.Request().Form.Get("alertType")
	lv := r.Request().Form.Get("lastVisited")
	var lastVisited time.Time
	var contentChain []publicModels.ContentForHandlebars
	var err error

	if len(lv) > 0 {
		lastVisited, err = time.Parse(time.RFC3339Nano, lv)
	}

	contentChain, err = publicServices.GetHTMLAlerts(r, alertType, lastVisited)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	response := utils.ResponseObject{
		Results:   contentChain,
		ResultSet: utils.ResponseResultSet{},
	}

	utils.ResponseJson(w, response.ToMap(), http.StatusOK)
}

func (cc ContentController) GetCalendarFeed(w http.ResponseWriter, r *shared.AppContext) {
	//Allow Anonymous
	privacyLevel := r.PublicAccount().PrivacyLevel

	acc, err := auth_links.AuthenticateAccountFromToken(r.Request(), r.TenantDatabase())
	if err != nil {
		r.Logger().Err(err).Msg("Failed to process a token. ")
	}

	if acc != nil {
		privacyLevel = acc.PrivacyLevel
		if r.PublicAccount().ID != uuid.Nil && r.PublicAccount().ID != acc.ID {
			r.Logger().Warn().Msgf("Overriding account. Account mismatch: %s != %s", r.Account().ID, acc.ID)
		}
	}

	tenantDB := r.TenantDatabase()
	calendar := ics.NewCalendar()
	calendar.SetMethod(ics.MethodPublish)
	calendar.SetRefreshInterval("PT24H")

	logger := logging.FromRequest(r.Request())

	params := publiccontent.ParamsFromMap(r.Request().Form)
	params.PrivacyLevel = privacyLevel

	params.SiteId = r.CurrentSiteID()
	params.ContentType = []publiccontent.Type{publiccontent.Event}
	params.IncludeTags = true
	params.Limit = 9999

	eventChain, err := publicServices.GetContentByParams(tenantDB, params)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	for _, content := range eventChain {
		var settings map[string]interface{}
		json.Unmarshal(content.Settings, &settings)

		event := calendar.AddEvent(content.ID.String())
		event.SetCreatedTime(content.Created)
		eventInfo, err := content.GetEventInfo()
		if err != nil {
			logger.Err(err).
				Str("contentId", content.ID.String()).
				Str("siteId", r.CurrentSiteID().String()).
				Msgf("failed to marshal event info from content in /api/v1/feed")
			continue
		}
		if eventInfo.IsAllDay {
			event.SetAllDayStartAt(eventInfo.StartDate)
			event.SetAllDayEndAt(eventInfo.EndDate)
		} else {
			event.SetStartAt(eventInfo.StartDate)
			event.SetEndAt(eventInfo.EndDate)
		}
		if len(eventInfo.RRule) > 0 {
			event.AddRrule(eventInfo.RRule[6:])
		}
		if eventInfo.Location.DisplayName != "" {
			event.SetLocation(eventInfo.Location.DisplayName)
		}

		event.SetDtStampTime(content.Updated)
		event.SetSummary(content.Title)
		route := content.Route
		if !strings.Contains(route, "http") {
			route = r.Request().Host + route
		}
		event.SetURL(route)
	}

	w.Write([]byte(calendar.Serialize()))
}

func (cc ContentController) GetNews(w http.ResponseWriter, r *shared.AppContext) {
	params := publiccontent.ParamsFromMap(r.Request().Form)
	params.PrivacyLevel = r.PublicAccount().PrivacyLevel
	params.SiteId = r.CurrentSiteID()

	// overriding defaults to true
	params.IncludeTags = converters.ToBool(mapxx.GetFirst(r.Request().Form, "includeTags"), true)
	params.IncludeMedia = converters.ToBool(mapxx.GetFirst(r.Request().Form, "includeMedia"), true)

	contents, e := publicServices.GetContentByParams(r.TenantDatabase(), params)

	response := utils.ResponseObject{
		Results: contents,
		ResultSet: utils.ResponseResultSet{
			TotalRecords: -1,
			Offset:       params.Skip,
			Limit:        params.Limit,
		},
	}

	utils.WriteResponseJSON(w, response, e)
}

func checkRedirects(db *gorm.DB, url *url.URL) (*url.URL, bool) {
	lr := commonServices.ILegacyUrlService()
	lu, lOk := lr.GetNewRoute(db, url)
	if lOk {
		if !equal(url, lu) {
			return lu, true
		}
	}

	return url, false
}

// equal To avoid infinite redirects to the same url we need to ensure we redirect to different destinations.
// (`from` is always absolute)
func equal(from, to *url.URL) bool {
	if to.IsAbs() {
		return urls.EqualNorms(from, to)
	} else {
		return urls.EqualPathsNorms(from, to)
	}
}

// IsStagingDomain can be used to determine if the request is coming from a .test/.cmdesign.imagineeverything domain
func IsStagingDomain(requestURI string) bool {
	switch {
	case strings.Index(requestURI, conf.DesignSiteHostSignature) > 0:
		return true
	case strings.Index(requestURI, conf.TestSiteHostSignature) > 0:
		return true
	case strings.Index(requestURI, "?hash=") > 0:
		return true
	default:
		return false
	}
}
func isRequestForPageType(path string) bool {
	if segments := strings.Split(path, "."); len(segments) > 1 {
		check := segments[len(segments)-1]
		for _, v := range []string{"ico", "js", "css", "json", "scss", "map"} {
			if check == v {
				return false
			}
		}
	}
	return true
}

func ApplyStagingHeaders(w http.ResponseWriter, r *shared.AppContext) {
	if IsStagingDomain(r.Request().Host + r.Request().RequestURI) {
		w.Header().Set("X-Robots-Tag", "noindex, noarchive")
		w.Header().Set("Cache-Control", "no-store")
	}
}

func WriteWithStandardHeaders(w http.ResponseWriter, r *shared.AppContext, data []byte) {
	var parts = strings.Split(r.Request().URL.Path, ".")
	if len(parts) > 1 {
		switch parts[len(parts)-1] {
		case "css":
			w.Header().Set("Content-Type", "text/css; charset=utf-8")
		case "js":
			w.Header().Set("Content-Type", "application/javascript; charset=utf-8")
		case "json":
			w.Header().Set("Content-Type", "application/javascript; charset=utf-8")
		case "xml":
			w.Header().Set("Content-Type", "text/xml; charset=utf-8")
		case "txt":
			w.Header().Set("Content-Type", "text/plain; charset=utf-8")
		default:
			token, _ := bcrypt.GenerateFromPassword([]byte(conf.BcryptUUIDTemplate), bcrypt.DefaultCost)
			w.Header().Set("Content-Type", "text/html; charset=utf-8")
			w.Header().Set("X-CSRF-Token", string(token))
		}
	} else {
		token, _ := bcrypt.GenerateFromPassword([]byte(conf.BcryptUUIDTemplate), bcrypt.DefaultCost)
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Header().Set("X-CSRF-Token", string(token))

		// Frame Ancestor is only available as a header value.
		w.Header().Set("Content-Security-Policy", csp.FrameAncestorDirective)
	}

	ApplyStagingHeaders(w, r)

	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(data)
}
