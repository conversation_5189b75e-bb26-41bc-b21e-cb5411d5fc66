import React, { Fragment, useEffect, useState } from 'react'
import { Card, CardContent } from '@mui/material'
import { CKEditor } from '@ckeditor/ckeditor5-react'
import ClassicEditor from 'ie-ckeditor5'
import { Get } from '../../helpers'
import WysiwygActions from './WysiwygActions'
import LockIcon from '@mui/icons-material/Lock'
import { initializeEditorInstance, patchEditorContentIfNeeded } from '../../helpers/ckeditor'
import { useAppContext } from '../../pkgs/auth/atoms'
import { DefaultEditorConfig } from '../../pkgs/lexical-editor/CKEditor'

/**
 * editorState = {
 *     state: Object{contentModel}
 *     hidden: bool,
 *     disabled: bool,
 *     wrapper: bool,
 *     children: Array[Element]
 *     setEditor: fn()
 * }
 * */
export function WysiwygCollection({
    editorState: { state, hideEditor, hideAll, disabled, wrapper, children, setEditor } = {},
    setActiveEditor,
    contentCollection = [],
    setContentCollection,
    editorTitle,
    allowMenuActions,
    openMediaGallery,
    openDocumentGallery
}) {
    const evaluators = useAppContext()
    const action = state?.active ? 'update' : 'create'
    console.log({
        action,
        state,
        disabled
    })

    function assignInstance(editor, index) {
        const c = contentCollection[index]
        const cc = contentCollection
        c.editor = editor
        cc[index] = c
        setContentCollection(cc)
    }

    useEffect(() => {
        // ensure popup / balloon panel is horizontally contained in the editor
        // vertically, if the popup is outside the editor its opacity is set to 50%
        const intervalId = setInterval(() => {
            try {
                const firstEditorContainer = document.getElementsByClassName('ck-editor__main')[0]
                const editorContainerBoundingClientRect = firstEditorContainer.getBoundingClientRect()
                const popups = document.getElementsByClassName('ck-balloon-panel')
                for (let popup of popups) {
                    const popupBoundingClientRect = popup.getBoundingClientRect()
                    popup.style.left = `${
                        (editorContainerBoundingClientRect.width - popupBoundingClientRect.width) / 2 +
                        editorContainerBoundingClientRect.left
                    }px`
                    const editorBottomEdgePosition =
                        editorContainerBoundingClientRect.y + editorContainerBoundingClientRect.height
                    const heightOfToolbar = 50
                    if (
                        popupBoundingClientRect.y - 10 > editorBottomEdgePosition ||
                        popupBoundingClientRect.y + heightOfToolbar < editorContainerBoundingClientRect.y
                    ) {
                        popup.style.opacity = '50%'
                    } else {
                        popup.style.opacity = '100%'
                    }
                }
            } catch (err) {
                // do nothing
            }
        }, 50)

        return () => {
            clearInterval(intervalId)
        }
    }, [])

    return (
        <Fragment>
            {state && (
                <Wrapper wrapper={wrapper} hidden={hideAll}>
                    <MainEditor
                        state={state}
                        setMainEditor={setEditor}
                        disabled={disabled}
                        hideEditor={hideEditor}
                        setActiveEditor={setActiveEditor}
                        openMediaGallery={openMediaGallery}
                        openDocumentGallery={openDocumentGallery}
                        action={action}
                    >
                        {children}
                    </MainEditor>
                </Wrapper>
            )}
            {contentCollection.map((child, index) => (
                <Card style={{ margin: '0.75em' }} key={child.id + child.title}>
                    <CardContent>
                        <WysiwygHeader
                            state={{
                                ...child,
                                title: editorTitle,
                                published:
                                    !child?.published && child.type === 'distributed_page' ? true : child.published
                            }}
                        />
                        {allowMenuActions && (
                            <WysiwygActions
                                disabled={!evaluators.action(child, 'update')}
                                openMediaGallery={() => {
                                    setActiveEditor(child.editor)
                                    openMediaGallery()
                                }}
                                openDocumentGallery={openDocumentGallery}
                            />
                        )}
                        <CKEditor
                            editor={ClassicEditor}
                            data={patchEditorContentIfNeeded(child.content, child.editor) || ''}
                            config={DefaultEditorConfig}
                            onReady={(editor) => {
                                console.log('WysiwygCollection')
                                assignInstance(editor, index)
                                editor.focus()
                            }}
                            // onFocus={(event, editor) => assignInstance(editor, index)}
                            disabled={!evaluators.action(child, 'update')}
                        />
                    </CardContent>
                </Card>
            ))}
        </Fragment>
    )
}

const Wrapper = ({ wrapper = false, hidden, children }) => {
    if (wrapper) {
        return (
            <>
                {!Boolean(hidden) && (
                    <Card style={{ margin: '0.75em', overflow: 'visible' }}>
                        <CardContent>{children}</CardContent>
                    </Card>
                )}
            </>
        )
    }
    return <>{!Boolean(hidden) && <>{children}</>}</>
}

const MainEditor = ({
    state,
    setActiveEditor,
    openMediaGallery,
    openDocumentGallery,
    setMainEditor,
    children,
    disabled,
    hideEditor,
    action
}) => {
    const evaluators = useAppContext()
    const [mainEditorRef, setMainEditorRef] = useState({})
    const mainHasPermission = evaluators.action(state, action)

    return (
        <Fragment>
            <WysiwygHeader state={state} disabled={!mainHasPermission} />
            {children && children}
            {!Boolean(hideEditor) && (
                <Fragment>
                    <WysiwygActions
                        openMediaGallery={() => {
                            setActiveEditor(mainEditorRef)
                            openMediaGallery()
                        }}
                        openDocumentGallery={openDocumentGallery}
                        disabled={!mainHasPermission || Boolean(disabled)}
                    />
                    <CKEditor
                        editor={ClassicEditor}
                        data={patchEditorContentIfNeeded(state.content, mainEditorRef) || ''}
                        config={DefaultEditorConfig}
                        onReady={(editor) => {
                            console.log('MainEditor ready')
                            // TODO => Distributed Content options appearing disabled
                            //  This sets all toolbars found to visually disabled, including distributed content boxes
                            initializeEditorInstance(editor, setMainEditor, mainHasPermission)
                            setMainEditorRef(editor)
                            editor.focus()
                        }}
                        disabled={!mainHasPermission || Boolean(disabled)}
                        className={!mainHasPermission || Boolean(disabled) ? 'disabled-editor' : ''}
                    />
                </Fragment>
            )}
        </Fragment>
    )
}
export default WysiwygCollection
const WysiwygHeader = ({ state, disabled }) => {
    return (
        <div className='flex-row-align-center'>
            <h2 className='sub-header'>{state.title}</h2>
            {!Get.isPublished(state) && <span className='material-icons-outlined'>edit_note</span>}
            {disabled && <LockIcon style={{ marginBottom: '0.20rem', color: 'grey' }} />}
        </div>
    )
}
