import * as React from 'react'
import { atom, useAtom, getDefaultStore } from 'jotai'
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from '@mui/material'
import type { DialogProps } from '@mui/material/Dialog'
import type { ButtonProps } from '@mui/material/Button'

type AnyValue = unknown

export type ConfirmAction<T = AnyValue> = {
    label: React.ReactNode
    value: T
    color?: ButtonProps['color']
    variant?: ButtonProps['variant']
    autoFocus?: boolean
    disabled?: boolean
}

export type ConfirmOptions<T = boolean> = {
    title?: React.ReactNode
    message?: React.ReactNode
    actions?: ConfirmAction<T>[]
    maxWidth?: DialogProps['maxWidth']
    fullWidth?: boolean
    dismissOnBackdropClick?: boolean // default: true
    dismissOnEscapeKey?: boolean // default: true

    // Convenience (used when actions not provided)
    confirmText?: React.ReactNode
    cancelText?: React.ReactNode
    danger?: boolean
}

type ConfirmRequest<T = AnyValue> = {
    id: number
    options: ConfirmOptions<T>
    resolve: (value: T) => void
}

const queueAtom = atom<ConfirmRequest<AnyValue>[]>([])

let idSeq = 1

function normalizeOptions<T>(opts: ConfirmOptions<T>): ConfirmOptions<T> {
    if (opts.actions && opts.actions.length > 0) return opts
    // Default to boolean confirm behavior
    const confirmColor: ButtonProps['color'] = opts.danger ? 'error' : 'primary'
    return {
        ...opts,
        actions: [
            { label: opts.cancelText ?? 'Cancel', value: false as unknown as T },
            {
                label: opts.confirmText ?? 'OK',
                value: true as unknown as T,
                variant: 'contained',
                color: confirmColor,
                autoFocus: true
            }
        ]
    }
}

export function ask<T = AnyValue>(opts: ConfirmOptions<T>): Promise<T> {
    const store = getDefaultStore()
    const options = normalizeOptions<T>(opts)
    return new Promise<T>((resolve) => {
        const req: ConfirmRequest<T> = { id: idSeq++, options, resolve }
        store.set(queueAtom, (q) => [...q, req as ConfirmRequest<AnyValue>])
    })
}

export function confirm(opts: ConfirmOptions<boolean> | React.ReactNode | string): Promise<boolean> {
    const options: ConfirmOptions<boolean> =
        typeof opts === 'string' || React.isValidElement(opts)
            ? { message: opts as React.ReactNode }
            : (opts as ConfirmOptions<boolean>)
    return ask<boolean>(options)
}

export function ConfirmProvider() {
    const [queue, setQueue] = useAtom(queueAtom)
    const current = queue[0]

    const handleResolve = (value: AnyValue) => {
        if (!current) return
        current.resolve(value)
        setQueue((q) => q.slice(1))
    }

    const handleClose = (_e: unknown, reason: 'backdropClick' | 'escapeKeyDown') => {
        if (!current) return
        const { options } = current
        if (reason === 'backdropClick' && options.dismissOnBackdropClick === false) return
        if (reason === 'escapeKeyDown' && options.dismissOnEscapeKey === false) return
        // Fallback to the first action's value (usually "Cancel")
        const fallback = options.actions?.[0]?.value
        handleResolve(fallback)
    }

    if (!current) return null
    const { options } = current
    const actions = options.actions ?? []

    return (
        <Dialog
            open
            onClose={handleClose}
            maxWidth={options.maxWidth ?? 'xs'}
            fullWidth={options.fullWidth ?? true}
            disableEscapeKeyDown={options.dismissOnEscapeKey === false}
        >
            {options.title && <DialogTitle>{options.title}</DialogTitle>}

            {options.message && (
                <DialogContent dividers>
                    {typeof options.message === 'string' ? <Typography>{options.message}</Typography> : options.message}
                </DialogContent>
            )}

            <DialogActions>
                {actions.map((a, i) => (
                    <Button
                        key={i}
                        onClick={() => handleResolve(a.value)}
                        color={a.color}
                        variant={a.variant}
                        autoFocus={a.autoFocus}
                        disabled={a.disabled}
                    >
                        {a.label}
                    </Button>
                ))}
            </DialogActions>
        </Dialog>
    )
}
