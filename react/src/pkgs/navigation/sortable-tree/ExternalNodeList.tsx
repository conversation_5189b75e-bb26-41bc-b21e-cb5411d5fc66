import React from 'react'
import { ExternalNode, ExternalNodeElement } from './ExternalNode_v1'
import { Get } from '../../../helpers/get'

export function ExternalNodeList({
    nodeList,
    deleteExternalNode,
    setNodeList,
    setVisible,
    confirmAction,
    isPermissible,
    style,
    hasSharedPagesPermission
}) {
    const canAccess = (node) => !(!hasSharedPagesPermission && Get.isPrimary(node))

    return (
        <div className='full-width no-margin'>
            {nodeList.length > 0 && (
                <div className='external-node-list mt10 mb10' style={style ? style : {}}>
                    {nodeList.map((node) => {
                        if (canAccess(node)) {
                            return (
                                <ExternalNode
                                    key={`${node.content_id}--${node.title}`}
                                    node={{ ...node, visible: setVisible }}
                                    deleteExternalNode={() => deleteExternalNode(node, nodeList, setNodeList)}
                                    confirmAction={confirmAction}
                                    isPermissible={isPermissible}
                                    hasSharedPagesPermission={hasSharedPagesPermission}
                                />
                            )
                        } else {
                            return (
                                <ExternalNodeElement
                                    key={`${node.content_id}--${node.title}`}
                                    disabled={true}
                                    node={node}
                                    Buttons={undefined}
                                    customClass={undefined}
                                />
                            )
                        }
                    })}
                </div>
            )}
        </div>
    )
}

export default ExternalNodeList
