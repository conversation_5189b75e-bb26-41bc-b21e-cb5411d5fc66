import { Autocomplete, Chip, FormControl, FormControlLabel, Stack, Switch, TextField } from '@mui/material'
import { useState } from 'react'
import { KeywordSearch } from '@/pkgs/search/promotions/types'

type KeywordSearchOrExtended<T extends KeywordSearch = KeywordSearch> = T | KeywordSearch

interface KeywordsInputProps {
    value: KeywordSearchOrExtended
    onChange: (value: KeywordSearchOrExtended) => void
    disabled?: boolean
    error?: string
}

export function KeywordsInput({ value, onChange, disabled, error }: KeywordsInputProps) {
    const [inputValue, setInputValue] = useState('')
    return (
        <Stack direction='row' spacing={2} alignItems={'center'}>
            <FormControl fullWidth>
                <Autocomplete
                    freeSolo
                    multiple
                    value={value.Keywords}
                    options={[]}
                    inputValue={inputValue} // Add state for this
                    onInputChange={(_, newInputValue) => {
                        setInputValue(newInputValue) // Add state for this
                    }}
                    renderTags={(tagValue, getTagProps) =>
                        tagValue.map((keyword, index) => (
                            <Chip label={keyword} {...getTagProps({ index })} key={index} disabled={disabled} />
                        ))
                    }
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            error={Boolean(error)}
                            helperText={error}
                            label='Keywords'
                            placeholder={value.Keywords.length === 0 ? 'Enter a keyword and press Enter' : ''}
                            disabled={disabled}
                            onBlur={() => {
                                if (inputValue) {
                                    const sanitized = inputValue.replace(/^[ ;,]+|[ ;,]+$/g, '')
                                    if (sanitized && !value.Keywords.includes(sanitized)) {
                                        onChange({
                                            ...value,
                                            Keywords: [...value.Keywords, sanitized]
                                        })
                                    }
                                }
                                setInputValue('') // Clear input this way
                            }}
                        />
                    )}
                    disabled={disabled}
                    onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                            if (inputValue) {
                                const sanitized = inputValue.replace(/^[ ;,]+|[ ;,]+$/g, '')
                                if (sanitized && !value.Keywords.includes(sanitized)) {
                                    onChange({
                                        ...value,
                                        Keywords: [...value.Keywords, sanitized]
                                    })
                                }
                                setInputValue('') // Clear input this way
                            }
                        }
                    }}
                    onChange={(_, newValue) => {
                        const sanitizedValues = newValue
                            .map((v) => (typeof v === 'string' ? v.replace(/^[ ;,]+|[ ;,]+$/g, '') : v))
                            .filter((v) => v)

                        onChange({
                            ...value,
                            Keywords: Array.from(new Set(sanitizedValues))
                        })
                    }}
                />
            </FormControl>
            <FormControlLabel
                sx={{ marginLeft: 'auto', minWidth: '150px' }}
                control={
                    <Switch
                        checked={value.ExactMatch}
                        onChange={(_, checked) =>
                            onChange({
                                ...value,
                                ExactMatch: checked
                            })
                        }
                        disabled={disabled}
                    />
                }
                labelPlacement={'start'}
                label='Exact Match'
            />
        </Stack>
    )
}
