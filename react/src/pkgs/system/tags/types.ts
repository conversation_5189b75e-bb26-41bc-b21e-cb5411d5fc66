import { paged, trackable } from '@/common/react-query'
import { z } from 'zod'

export const tagDto = z.object({
    Name: z.string(),
    Types: z.array(z.string())
})

export const tag = trackable.extend({
    ID: z.string(),
    Name: z.string(),
    Types: z.array(z.string()),
    Active: z.boolean()
})

export const tags = paged.extend({
    Rows: z.array(tag)
})

export type TagDTO = z.infer<typeof tagDto>

export enum TagType {
    Page = 'page',
    News = 'news',
    Event = 'event',
    Alert = 'alert',
    Fragment = 'fragment',
    List = 'list',
    Document = 'document',
    Image = 'image',
    Site = 'site'
}

export function getTagTypes() {
    return ['page', 'news', 'event', 'alert', 'fragment', 'list', 'document', 'image', 'site'] as const
}
