import React, { useCallback, useState } from 'react'
import { Alert, Box, Button, CircularProgress, LinearProgress, Paper, Typography } from '@mui/material'
import { styled } from '@mui/material/styles'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import { httpGet } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'

interface PostPolicyOutput {
    url: string
    fields: Record<string, string>
    expiresAt: string
}

interface UploadState {
    progress: number
    error?: string
    success?: boolean
}

const VisuallyHiddenInput = styled('input')`
    clip: rect(0 0 0 0);
    clip-path: inset(50%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    bottom: 0;
    left: 0;
    white-space: nowrap;
    width: 1px;
`

export interface S3UploaderProps {
    // Optional callback when upload is complete
    onUploadComplete?: (fileUrl: string) => void
    // Optional callback when upload fails
    onUploadError?: (error: string) => void
}

export const S3Uploader: React.FC<S3UploaderProps> = ({ onUploadComplete, onUploadError }) => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null)
    const [uploadState, setUploadState] = useState<UploadState>({
        progress: 0
    })
    const [isLoading, setIsLoading] = useState(false)

    const getPresignedPostPolicy = async (fileName, contentType) => {
        return await httpGet(
            `${BASE}/api/v1/aws/upload-policy`,
            { fileName, contentType },
            z.object({
                url: z.string(),
                fields: z.array(z.object({ key: z.string(), value: z.string() })),
                expiresAt: z.string()
            })
        )
    }

    const validateFile = (file: File): string | null => {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
        const maxSize = 1024 * 1024 // 1MB

        if (!allowedTypes.includes(file.type)) {
            return 'Only JPEG, PNG, GIF, and WebP images are allowed'
        }

        if (file.size > maxSize) {
            return 'File size must be less than 1MB'
        }

        return null
    }

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            const error = validateFile(file)
            if (error) {
                setUploadState({ progress: 0, error })
                onUploadError?.(error)
                return
            }
            setSelectedFile(file)
            setUploadState({ progress: 0 })
        }
    }

    const uploadFile = async () => {
        if (!selectedFile) return

        try {
            setIsLoading(true)
            const policy = await getPresignedPostPolicy(selectedFile.name, selectedFile.type)

            const formData = new FormData()

            // Add fields in the exact order they were sent from the backend
            policy.fields.forEach((field) => {
                formData.append(field.key, field.value)
            })

            // Add the content type
            formData.append('Content-Type', selectedFile.type)

            // Add the file last
            formData.append('file', selectedFile)

            const xhr = new XMLHttpRequest()

            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    const progress = (event.loaded / event.total) * 100
                    setUploadState({ progress })
                }
            }

            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4) {
                    setIsLoading(false)

                    if (xhr.status === 204) {
                        // Success - construct the file URL
                        const fileUrl = `${policy.url}/${policy.fields[0].value}`
                        setUploadState({ progress: 100, success: true })
                        onUploadComplete?.(fileUrl)
                    } else {
                        const error = 'Upload failed. Please try again.'
                        setUploadState({ progress: 0, error })
                        onUploadError?.(error)
                    }
                }
            }

            xhr.onerror = () => {
                setIsLoading(false)
                const error = 'Upload failed. Please check your connection.'
                setUploadState({ progress: 0, error })
                onUploadError?.(error)
            }

            xhr.open('POST', policy.url, true)
            xhr.send(formData)
        } catch (error) {
            setIsLoading(false)
            const errorMessage = error instanceof Error ? error.message : 'Upload failed'
            setUploadState({ progress: 0, error: errorMessage })
            onUploadError?.(errorMessage)
        }
    }

    const resetUpload = useCallback(() => {
        setSelectedFile(null)
        setUploadState({ progress: 0 })
    }, [])

    return (
        <Paper elevation={3} sx={{ p: 3, maxWidth: 500, mx: 'auto' }}>
            <Box display='flex' flexDirection='column' gap={2}>
                <Typography variant='h6' gutterBottom>
                    Upload Image
                </Typography>

                {uploadState.error && (
                    <Alert severity='error' onClose={() => setUploadState({ progress: 0 })}>
                        {uploadState.error}
                    </Alert>
                )}

                {uploadState.success && (
                    <Alert severity='success' onClose={resetUpload}>
                        File uploaded successfully!
                    </Alert>
                )}

                <Button component='label' variant='contained' startIcon={<CloudUploadIcon />} disabled={isLoading}>
                    Select Image
                    <VisuallyHiddenInput
                        type='file'
                        accept='image/jpeg,image/png,image/gif,image/webp,image/svg+xml'
                        onChange={handleFileSelect}
                    />
                </Button>

                {selectedFile && (
                    <Typography variant='body2' color='textSecondary'>
                        Selected: {selectedFile.name}
                    </Typography>
                )}

                {uploadState.progress > 0 && (
                    <Box sx={{ width: '100%' }}>
                        <LinearProgress variant='determinate' value={uploadState.progress} sx={{ mb: 1 }} />
                        <Typography variant='body2' color='textSecondary'>
                            {Math.round(uploadState.progress)}% uploaded
                        </Typography>
                    </Box>
                )}

                <Button
                    variant='contained'
                    color='primary'
                    onClick={uploadFile}
                    disabled={!selectedFile || isLoading || uploadState.success}
                    sx={{ mt: 2 }}
                >
                    {isLoading ? <CircularProgress size={24} color='inherit' /> : 'Upload'}
                </Button>
            </Box>
        </Paper>
    )
}
