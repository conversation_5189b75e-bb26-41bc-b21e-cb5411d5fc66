import { FileSelector } from '@/pkgs/storage/uploader/FileSelector'
import { useState } from 'react'
import { FileCard } from '@/pkgs/storage/uploader/FileCard'
import CMDialog from '@/common/components/CMDialog'

type UploaderProps = {
    type: 'image' | 'document'
    multiple: boolean
    onError: (error: string, file: File) => void
    disabled?: boolean
    sizeLimit?: number
    buttonText?: string
}

export const Uploader = ({ type, multiple, onError, disabled, sizeLimit }: UploaderProps) => {
    const [files, setFiles] = useState<File[]>([])
    const onFileSelect = (file: File) => {
        setFiles([...files, file])
    }

    return (
        <div>
            <FileSelector type='image' multiple={false} onFileSelect={onFileSelect} />
            {files.map((file) => (
                <FileCard
                    key={file.name}
                    file={file}
                    type={type}
                    disabled={!!disabled}
                    onUpload={() => {}}
                    onDelete={() => {
                        setFiles(files.filter((f) => f.name !== file.name))
                    }}
                />
            ))}
        </div>
    )
}

type UploaderDialogProps = UploaderProps & {
    onClose: () => void
    open: boolean
}

export const UploaderDialog = ({ onClose, open, ...props }: UploaderDialogProps) => {
    return (
        <CMDialog open={open} onClose={onClose}>
            <Uploader {...props} />
        </CMDialog>
    )
}
