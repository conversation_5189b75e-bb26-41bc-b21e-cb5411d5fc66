import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import { Button } from '@mui/material'
import React from 'react'
import { styled } from '@mui/system'
import { notify } from '@/helpers'

type FileSelectorProps = {
    type: 'image' | 'document'
    onFileSelect: (file: File) => void
    multiple: boolean
    onError?: (error: string, file: File) => void
    disabled?: boolean
    sizeLimit?: number
    buttonText?: string
}

export const FileSelector = ({
    type,
    onFileSelect,
    sizeLimit = -1,
    multiple,
    onError,
    buttonText,
    disabled
}: FileSelectorProps) => {
    sizeLimit = sizeLimit > 0 ? sizeLimit : type === 'image' ? 1024 * 1024 * 10 : 1024 * 1024 * 100

    const allowedTypes =
        type === 'image'
            ? ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
            : [
                  'application/pdf',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                  'application/vnd.ms-powerpoint',
                  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                  'application/vnd.ms-excel',
                  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
              ]

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files
        if (!files || files.length === 0) return

        // Validate each selected file
        for (let i = 0; i < files.length; i++) {
            const file = files[i]

            // Check file type
            if (!allowedTypes.includes(file.type)) {
                const errorMsg = `Invalid file type: ${file.type}. Allowed types: ${allowedTypes.join(', ')}`
                notify(errorMsg, 'error')

                if (onError) {
                    onError(errorMsg, file)
                }
                continue
            }

            // Check file size
            if (file.size > sizeLimit) {
                const sizeLimitMB = (sizeLimit / (1024 * 1024)).toFixed(1)
                const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1)

                const errorMsg = `File too large: ${fileSizeMB}MB. Maximum allowed: ${sizeLimitMB}MB`
                notify(errorMsg, 'error')

                if (onError) {
                    onError(errorMsg, file)
                }
                continue
            }

            // File is valid, call the callback
            onFileSelect(file)
        }

        // Clear the input to allow selecting the same file again
        event.target.value = ''
    }

    return (
        <Button component='label' variant='contained' startIcon={<CloudUploadIcon />} disabled={!!disabled}>
            {buttonText || (type === 'image' ? 'Select Image' : 'Select Document')}
            <VisuallyHiddenInput
                type='file'
                accept={allowedTypes.join(',')}
                onChange={handleFileSelect}
                multiple={multiple}
            />
        </Button>
    )
}

const VisuallyHiddenInput = styled('input')`
    clip: rect(0 0 0 0);
    clip-path: inset(50%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    bottom: 0;
    left: 0;
    white-space: nowrap;
    width: 1px;
`
