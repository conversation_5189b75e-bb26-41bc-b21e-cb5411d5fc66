import React, { useEffect, useState } from 'react'
import { Autocomplete, TextField, Chip } from '@mui/material'
import { FilterComponentType } from '@/pkgs/queries/types'

export const FilterMultiString: FilterComponentType<string[]> = ({ value, onChange, config }) => {
    value = value || []
    useEffect(() => {
        if (value === undefined) {
            onChange([])
        }
    }, [value])

    // Keep track of the input value for free text entry
    const [inputValue, setInputValue] = useState('')

    // Get options from config or use empty array as default
    const options = config?.options || []

    // Combine predefined options with existing values to show all in dropdown
    const allOptions = Array.from(new Set([...options, ...(value || [])]))

    return (
        <Autocomplete
            multiple
            value={value}
            onChange={(event, newValue) => {
                onChange(newValue)
            }}
            onBlur={() => {
                inputValue && onChange([...(value || []), inputValue])
                setInputValue('')
            }}
            inputValue={inputValue}
            onInputChange={(event, newInputValue) => {
                setInputValue(newInputValue)
            }}
            options={allOptions}
            freeSolo
            renderTags={(value: string[], getTagProps) =>
                value.map((option: string, index: number) => (
                    <Chip variant='outlined' label={option} {...getTagProps({ index })} key={index} />
                ))
            }
            renderInput={(params) => (
                <TextField
                    {...params}
                    variant='outlined'
                    placeholder={value.length === 0 ? 'Enter a string and press Enter...' : ''}
                    size='small'
                />
            )}
            size='small'
            sx={{
                minWidth: 200,
                '& .MuiOutlinedInput-root': {
                    padding: '3px 9px'
                }
            }}
        />
    )
}
