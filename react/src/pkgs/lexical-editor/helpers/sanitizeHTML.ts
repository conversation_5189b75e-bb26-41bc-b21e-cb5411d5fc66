import DOMPurify from 'dompurify'

// Initialize DOMPurify for different environments
const purify = (() => {
    if (typeof window !== 'undefined') {
        // Browser environment
        return DOMPurify
    } else {
        // Node.js/test environment - use the global window provided by jsdom
        return DOMPurify(window as any)
    }
})()

const invalidEmptyTags = new Set(['TABLE', 'THEAD', 'TBODY', 'TFOOT', 'TR', 'UL', 'OL', 'DL'])
const inlineTags = new Set([
    'A',
    'ABBR',
    'ACRONYM',
    'B',
    'BR',
    'BUTTON',
    'CITE',
    'EM',
    'I',
    'IMG',
    'SMALL',
    'SPAN',
    'STRONG',
    'SUB',
    'SUP'
])

const blockTags = new Set([
    'ADDRESS',
    'ARTICLE',
    'ASIDE',
    'BLOCKQUOTE',
    'CANVAS',
    'DD',
    'DIV',
    'DL',
    'DT',
    'FIELDSET',
    'FIGCAPTION',
    'FIGURE',
    'FOOTER',
    'FORM',
    'H1',
    'H2',
    'H3',
    'H4',
    'H5',
    'H6',
    'HEADER',
    'HR',
    'LI',
    'MAIN',
    'NAV',
    'NOSCRIPT',
    'OL',
    'P',
    'PRE',
    'SECTION',
    'TABLE',
    'TFOOT',
    'UL',
    'VIDEO'
])

export function sanitizeHTML(htmlString: string) {
    // Child nodes inside iframe/div gets deleted sometimes
    // So, we add inner html to parent node and re-apply "uponSanitizeElement"
    purify.addHook('afterSanitizeElements', (node: Node) => {
        // Type guard to check if node is an Element
        if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element

            if ((element.tagName === 'IFRAME' || element.tagName === 'DIV') && element.getAttribute('data-tmp-html')) {
                const htmlElement = element as HTMLElement
                htmlElement.innerHTML = element.getAttribute('data-tmp-html')!
                element.removeAttribute('data-tmp-html')
                return
            }

            // Remove specific empty elements
            if (invalidEmptyTags.has(element.nodeName)) {
                if (isNodeEmpty(element)) {
                    element.parentNode?.removeChild(element)
                    return
                }
            }

            // Wrap inline elements not inside block-level elements
            if (inlineTags.has(element.nodeName)) {
                let parent = element.parentElement

                while (parent && !blockTags.has(parent.nodeName) && parent.nodeName !== 'BODY') {
                    parent = parent.parentElement
                }

                if (!parent || (parent && parent.nodeName === 'BODY')) {
                    const p = element.ownerDocument.createElement('p')

                    element.parentNode?.replaceChild(p, element)
                    p.appendChild(element)
                }
            }
        }
    })

    purify.addHook('uponSanitizeElement', function (currentNode: Node, data: any, config: any) {
        // Type guard to check if currentNode is an Element
        if (currentNode.nodeType === Node.ELEMENT_NODE) {
            const element = currentNode as Element
            const htmlElement = element as HTMLElement

            if ((element.tagName === 'IFRAME' || element.tagName === 'DIV') && htmlElement.innerHTML) {
                element.setAttribute('data-tmp-html', htmlElement.innerHTML)
                htmlElement.innerHTML = ''
            }
        }
    })

    const sanitizedHTML = purify.sanitize(htmlString, {
        SAFE_FOR_TEMPLATES: false,
        // USE_PROFILES: { html: true },
        ADD_TAGS: [
            'figure',
            'div',
            'br',
            'ie-fragment',
            'ie-document',
            'iframe',
            'oembed',
            'a',
            'figure',
            'oembed',
            'a',
            'td',
            'tr',
            'th',
            'h1',
            'h2',
            'h3',
            'h4',
            'h5',
            'h6',
            'ul',
            'li'
        ],
        ALLOW_SELF_CLOSE_IN_ATTR: true,
        ADD_ATTR: [
            'target',
            'style',
            'data-lexical-document-id',
            'data-lexical-video-id',
            'data-lexical-youtube',
            'data-lexical-collapsible-content',
            'data-lexical-content-fragment',
            'monaco-code-embed',
            'url'
        ]
    })

    purify.removeAllHooks()
    console.log('sanitizedHTML')
    console.log(sanitizedHTML)
    return sanitizedHTML
}

function isNodeEmpty(node: Element): boolean {
    if (node.children.length > 0) {
        for (let i = 0; i < node.children.length; i++) {
            const child = node.children[i]
            if (!invalidEmptyTags.has(child.nodeName)) {
                return false
            }
            if (!isNodeEmpty(child)) {
                return false
            }
        }
    }

    return true
}
