{
    "env": {
        "browser": true,
        "es2021": true
    },
    "extends": ["plugin:react/recommended", "plugin:import/typescript", "plugin:prettier/recommended"],
    "parser": "@typescript-eslint/parser",
    "overrides": [],
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module",
        "project": "./tsconfig.json"
    },
    "plugins": ["react", "prettier"],
    "settings": {
        "react": {
            "version": "detect"
        }
    },
    "rules": { // off | warn | error
        "prettier/prettier": "off", // TODO: enable
        "react/jsx-no-duplicate-props": "error",
        "react/no-unknown-property": "error",
        "react/no-children-prop": "error",
        "react/jsx-key": "error",
        "@typescript-eslint/triple-slash-reference": "off",
        "react/react-in-jsx-scope": "off",
        "react/prop-types": 0, // until we migrate completely to TS
        "react/display-name": 0, // displayName is used in react-devtools
        "react/jsx-no-target-blank": 0,
        "react/no-unescaped-entities": 0
    }
}
